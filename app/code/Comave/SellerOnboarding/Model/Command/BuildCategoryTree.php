<?php

declare(strict_types=1);

namespace Comave\SellerOnboarding\Model\Command;

use Magento\Catalog\Model\ResourceModel\Category\CollectionFactory;
use Magento\Framework\DB\Select;

class BuildCategoryTree
{
    private array $pathMapping = [];

    /**
     * @param CollectionFactory $collectionFactory
     */
    public function __construct(private readonly CollectionFactory $collectionFactory)
    {
    }

    /**
     * @param string $categoryTreePath
     * @return string
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function execute(string $categoryTreePath): string
    {
        if (empty($categoryTreePath)) {
            return '';
        }

        if (isset($this->pathMapping[$categoryTreePath])) {
            return $this->pathMapping[$categoryTreePath];
        }

        $collection = $this->collectionFactory->create();
        $collection->getSelect()
            ->reset(Select::COLUMNS)
            ->columns(['entity_id']);
        $collection->addFieldToFilter(
            'entity_id',
            [
                'in' => explode('/', $categoryTreePath)
            ]
        );
        $collection->addAttributeToSelect(['name'], 'inner');

        $pathData = $collection->getConnection()->fetchAssoc($collection->getSelect());
        $this->pathMapping[$categoryTreePath] = empty($pathData) ?
            '' :
            implode(' > ', array_column($pathData, 'name'));

        return $this->pathMapping[$categoryTreePath];
    }
}
