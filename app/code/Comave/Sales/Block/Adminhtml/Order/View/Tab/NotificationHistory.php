<?php
/**
 * Copyright © Commercial Avenue
 */
declare(strict_types=1);

namespace Comave\Sales\Block\Adminhtml\Order\View\Tab;

use Comave\Sales\Model\Order\NotificationUiCollectionProvider;
use Magento\Backend\Block\Template;
use Magento\Backend\Block\Template\Context;
use Magento\Backend\Block\Widget\Tab\TabInterface;
use Magento\Framework\Phrase;
use Magento\Framework\Registry;
use Magento\Sales\Model\Order;

class NotificationHistory extends Template implements TabInterface
{
    /**
     * Template
     *
     * @var string
     */
    protected $_template = 'Comave_Sales::order/view/tab/notification_history.phtml';

    /**
     * @param \Magento\Backend\Block\Template\Context $context
     * @param \Magento\Framework\Registry $registry
     * @param array $data
     */
    public function __construct(
        private readonly NotificationUiCollectionProvider $collectionProvider,
        private readonly Context $context,
        private readonly Registry $registry,
        array $data = []
    ) {
        parent::__construct($context, $data);
    }

    /**
     * Retrieve order model instance
     *
     * @return \Magento\Sales\Model\Order
     */
    public function getOrder(): Order
    {
        return $this->registry->registry('current_order');
    }

    /**
     * Retrieve order model instance
     *
     * @return int
     *Get current id order
     */
    public function getOrderId(): int
    {
        return (int)$this->getOrder()->getEntityId();
    }

    /**
     * Retrieve order increment id
     *
     * @return string
     */
    public function getOrderIncrementId(): string
    {
        return $this->getOrder()->getIncrementId();
    }

    /**
     * Retrieve order increment id
     *
     * @return string
     */
    public function getCustomerEmail(): string
    {
        return $this->getOrder()->getCustomerEmail();
    }

    /**
     * {@inheritdoc}
     */
    public function getTabLabel(): Phrase|string
    {
        return __('Notification History');
    }

    /**
     * {@inheritdoc}
     */
    public function getTabTitle()
    {
        return __('Notification History');
    }

    /**
     * {@inheritdoc}
     */
    public function canShowTab(): bool
    {
        return true;
    }

    /**
     * {@inheritdoc}
     */
    public function isHidden(): bool
    {
        return false;
    }

    /**
     * @param int $orderId
     * @return array
     */
    public function getNotificationHistory(int $orderId): array
    {
        return $this->collectionProvider->getByOrder($orderId);
    }
}
