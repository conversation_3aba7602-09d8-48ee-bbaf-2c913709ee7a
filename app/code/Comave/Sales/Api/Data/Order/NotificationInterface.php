<?php
/**
 * Copyright © Commercial Avenue
 */
declare(strict_types=1);

namespace Comave\Sales\Api\Data\Order;

use Magento\Framework\Api\ExtensibleDataInterface;

interface NotificationInterface extends ExtensibleDataInterface
{
    public const string NOTIFICATION_ID = 'notification_id';
    public const string ORDER_ID = 'order_id';
    public const string MESSAGE = 'message';
    public const string CREATED_AT = 'created_at';

    /**
     * @param $id
     * @return self
     */
    public function setId($id);

    /**
     * @return int
     */
    public function getId();

    /**
     * @param int $id
     * @return self
     */
    public function setNotificationId(int $id): self;

    /**
     * @return int|null
     */
    public function getNotificationId(): ?int;

    /**
     * @param int $orderId
     * @return self
     */
    public function setOrderId(int $orderId): self;

    /**
     * Get Order ID
     *
     * @return int
     */
    public function getOrderId(): int;

    /**
     * @param string $message
     * @return self
     */
    public function setMessage(string $message): self;

    /**
     * Get Notification Message
     *
     * @return string
     */
    public function getMessage(): string;
}