<?php
/**
 * Copyright © Commercial Avenue
 */
declare(strict_types=1);

namespace Comave\Sales\Api\Data\Order;

use Magento\Framework\Api\SearchCriteriaInterface;

interface NotificationSearchResultInterface
{
    /**
     * get items
     *
     * @return \Comave\Sales\Api\Data\Order\NotificationInterface[]
     */
    public function getItems();

    /**
     * Set items
     *
     * @param \Comave\Sales\Api\Data\Order\NotificationInterface[] $items
     * @return $this
     */
    public function setItems(array $items);

    /**
     * @param SearchCriteriaInterface $searchCriteria
     * @return $this
     */
    public function setSearchCriteria(SearchCriteriaInterface $searchCriteria);

    /**
     * @param int $count
     * @return $this
     */
    public function setTotalCount($count);
}
