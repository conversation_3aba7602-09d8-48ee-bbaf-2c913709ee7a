<?php
/**
 * Copyright © Commercial Avenue
 */
declare(strict_types=1);

namespace Comave\Sales\Api\Order;

use Comave\Sales\Api\Data\Order\NotificationInterface;

interface NotificationRepositoryInterface
{
    /**
     * @param \Comave\Sales\Api\Data\Order\NotificationInterface $notification
     * @return \Comave\Sales\Api\Data\Order\NotificationInterface
     */
    public function save(NotificationInterface $notification);

    /**
     * @param int $notificationId
     * @return \Comave\Sales\Api\Data\Order\NotificationInterface
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    public function get(int $notificationId);

    /**
     * @param \Comave\Sales\Api\Data\Order\NotificationInterface $notification
     * @return bool true on success
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function delete(NotificationInterface $notification);

    /**
     * @param int $notificationId
     * @return bool true on success
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function deleteById(int $notificationId);

    /**
     * Clear caches instances
     *
     * @return void
     */
    public function clear(): void;
}