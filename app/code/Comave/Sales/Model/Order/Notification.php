<?php
/**
 * Copyright © Commercial Avenue
 */
declare(strict_types=1);

namespace Comave\Sales\Model\Order;

use Comave\Sales\Api\Data\Order\NotificationInterface;
use Comave\Sales\Model\ResourceModel\Order\Notification as OrderNotificationResourceModel;
use Magento\Framework\Api\ExtensionAttributesInterface;
use Magento\Framework\Model\AbstractExtensibleModel;

class Notification extends AbstractExtensibleModel implements NotificationInterface
{
    /**
     * Cache tag
     *
     * @var string
     */
    public const string CACHE_TAG = 'sales_order_notification';
    /**
     * Cache tag
     *
     * @var string
     * phpcs:disable PSR2.Classes.PropertyDeclaration.Underscore,PSR12.Classes.PropertyDeclaration.Underscore
     */
    protected $_cacheTag = self::CACHE_TAG;
    /**
     * Event prefix
     *
     * @var string
     */
    protected $_eventPrefix = 'sales_order_notification';
    /**
     * Event object
     *
     * @var string
     */
    protected $_eventObject = 'sales_order_notification';
    //phpcs:enable

    /**
     * Get identities
     *
     * @return array
     */
    public function getIdentities(): array
    {
        return [sprintf("%s_%s", self::CACHE_TAG, $this->getId())];
    }

    /**
     * @return \Magento\Framework\Api\ExtensionAttributesInterface|null
     */
    public function getExtensionAttributes(): ?ExtensionAttributesInterface
    {
        return $this->_getExtensionAttributes();
    }

    /**
     * @param \Magento\Framework\Api\ExtensionAttributesInterface $extensionAttributes
     * @return \Comave\SellerOnboarding\Model\Category\Mapping
     */
    public function setExtensionAttributes(ExtensionAttributesInterface $extensionAttributes): Notification
    {
        return $this->_setExtensionAttributes($extensionAttributes);
    }

    /**
     * Initialize resource model
     *
     * @return void
     * phpcs:disable PSR2.Methods.MethodDeclaration.Underscore, PSR12.Methods.MethodDeclaration.Underscore
     */
    protected function _construct()
    {
        $this->_init(OrderNotificationResourceModel::class);
    }

    /**
     * @param int $id
     * @return \Comave\SellerOnboarding\Api\Data\Category\MappingInterface
     */
    public function setNotificationId(int $id): NotificationInterface
    {
        return $this->setData(self::NOTIFICATION_ID, $id);
    }

    /**
     * @return int
     */
    public function getNotificationId(): int
    {
        return (int)$this->getData(self::NOTIFICATION_ID);
    }

    /**
     * @param int $orderId
     * @return self
     */
    public function setOrderId(int $orderId): NotificationInterface
    {
        return $this->setData(self::ORDER_ID, $orderId);
    }

    /**
     * Get Order ID
     *
     * @return int
     */
    public function getOrderId(): int
    {
        return (int)$this->getData(self::ORDER_ID);
    }

    /**
     * @param string $message
     * @return self
     */
    public function setMessage(string $message): NotificationInterface
    {
        return $this->setData(self::MESSAGE, $message);
    }

    /**
     * Get Notification Message
     *
     * @return string
     */
    public function getMessage(): string
    {
        return (string)$this->getData(self::MESSAGE);
    }
}
