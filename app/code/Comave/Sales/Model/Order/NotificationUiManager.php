<?php
/**
 * Copyright © Commercial Avenue
 */
declare(strict_types=1);

namespace Comave\Sales\Model\Order;

use Comave\Sales\Api\Order\NotificationListRepositoryInterface;
use Comave\Sales\Api\Order\NotificationRepositoryInterface;
use Comave\Sales\Api\Data\Order\NotificationInterface;
use Comave\Sales\Model\Order\Notification;
use Comave\Sales\Model\Order\NotificationFactory;
use Magento\Framework\Api\SearchCriteriaBuilder;
use Magento\Framework\Model\AbstractModel;
use Umc\Crud\Ui\EntityUiManagerInterface;

class NotificationUiManager implements EntityUiManagerInterface
{
    /**
     * @param \Magento\Framework\Api\SearchCriteriaBuilder $searchCriteriaBuilder
     * @param \Comave\Sales\Api\Order\NotificationListRepositoryInterface $listRepository
     * @param \Comave\Sales\Api\Order\NotificationRepositoryInterface $repository
     * @param \Comave\Sales\Model\Order\NotificationFactory $factory
     */
    public function __construct(
        private readonly SearchCriteriaBuilder $searchCriteriaBuilder,
        private readonly NotificationListRepositoryInterface $listRepository,
        private readonly NotificationRepositoryInterface $repository,
        private readonly NotificationFactory $factory
    ) {
    }

    /**
     * @param \Magento\Framework\Model\AbstractModel $notification
     * @return void
     */
    public function save(AbstractModel $notification)
    {
        $this->repository->save($notification);
    }

    /**
     * @param int $id
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     * @throws \Magento\Framework\Exception\CouldNotDeleteException
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function delete(int $id)
    {
        $this->repository->deleteById($id);
    }

    /**
     * @param int|null $id
     * @return \Magento\Framework\Model\AbstractModel | Notification | NotificationInterface;
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    public function get(?int $id)
    {
        return ($id)
            ? $this->repository->get($id)
            : $this->factory->create();
    }

    /**
     * @return array
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function getList(): array
    {
        return $this->listRepository->getList($this->searchCriteriaBuilder->create())->getItems();
    }
}
