<?php
/**
 * Copyright © Commercial Avenue
 */
declare(strict_types=1);

namespace Comave\Sales\Model\Order;

use Comave\Sales\Api\Order\NotificationRepositoryInterface;
use Comave\Sales\Api\Data\Order\NotificationInterface;
use Comave\Sales\Api\Data\Order\NotificationInterfaceFactory;
use Comave\Sales\Model\ResourceModel\Order\Notification as NotificationResourceModel;
use Exception;
use Magento\Framework\Exception\CouldNotDeleteException;
use Magento\Framework\Exception\CouldNotSaveException;
use Magento\Framework\Exception\NoSuchEntityException;

class NotificationRepository implements NotificationRepositoryInterface
{
    /**
     * @var NotificationInterface[]
     */
    private array $cache = [];

    /**
     * @param NotificationInterfaceFactory $factory
     * @param NotificationResourceModel $resource
     */
    public function __construct(
        private readonly NotificationInterfaceFactory $factory,
        private readonly NotificationResourceModel $resource
    ) {
    }

    /**
     * @param \Comave\Sales\Api\Data\Order\NotificationInterface $notification
     * @return \Comave\Sales\Api\Data\Order\NotificationInterface
     * @throws \Magento\Framework\Exception\CouldNotSaveException
     */
    public function save(NotificationInterface $notification)
    {
        try {
            $this->resource->save($notification);
        } catch (Exception $exception) {
            throw new CouldNotSaveException(
                __($exception->getMessage())
            );
        }

        return $notification;
    }

    /**
     * @param int $notificationId
     * @return true
     * @throws \Magento\Framework\Exception\CouldNotDeleteException
     * @throws \Magento\Framework\Exception\LocalizedException
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    public function deleteById(int $notificationId)
    {
        return $this->delete($this->get($notificationId));
    }

    /**
     * @param \Comave\Sales\Api\Data\Order\NotificationInterface $notification
     * @return true
     * @throws \Magento\Framework\Exception\CouldNotDeleteException
     */
    public function delete(NotificationInterface $notification)
    {
        try {
            $id = $notification->getNotificationId();
            $this->resource->delete($notification);
            unset($this->cache[$id]);
        } catch (Exception $exception) {
            throw new CouldNotDeleteException(
                __($exception->getMessage())
            );
        }

        return true;
    }

    /**
     * @param int $notificationId
     * @return \Comave\Sales\Api\Data\Order\NotificationInterface
     * @throws \Magento\Framework\Exception\LocalizedException
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    public function get(int $notificationId)
    {
        if (!isset($this->cache[$notificationId])) {
            $OrderSourceData = $this->factory->create();
            $this->resource->load($OrderSourceData, $notificationId);
            if (!$OrderSourceData->getId()) {
                throw new NoSuchEntityException(
                    __('The Order Notification with the ID "%1" does not exist . ', $notificationId)
                );
            }
            $this->cache[$notificationId] = $OrderSourceData;
        }

        return $this->cache[$notificationId];
    }

    /**
     * @inheritDoc
     */
    public function clear(): void
    {
        $this->cache = [];
    }
}
