<?php
/**
 * Copyright © Commercial Avenue
 */
declare(strict_types=1);

namespace Comave\Sales\Model\Order;

use Comave\Sales\Api\Data\Order\NotificationInterface;
use Comave\Sales\Model\ResourceModel\Order\Notification\CollectionFactory;
use Magento\Framework\Model\ResourceModel\Db\Collection\AbstractCollection;
use Umc\Crud\Ui\CollectionProviderInterface;

class NotificationUiCollectionProvider implements CollectionProviderInterface
{
    /**
     * @param CollectionFactory $factory
     */
    public function __construct(private readonly CollectionFactory $factory)
    {
    }

    /**
     * @return \Magento\Framework\Model\ResourceModel\Db\Collection\AbstractCollection
     */
    public function getCollection(): AbstractCollection
    {
        return $this->factory->create();
    }

    /**
     * @param int $orderId
     * @return array
     */
    public function getByOrder(int $orderId): array
    {
        $notificationCollection = $this->getCollection();
        $notificationCollection->addFieldToFilter(NotificationInterface::ORDER_ID, ['eq' => $orderId]);

        return $notificationCollection->getItems();
    }
}
