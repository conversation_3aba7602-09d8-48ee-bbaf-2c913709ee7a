<?php
/**
 * Copyright © Commercial Avenue
 */
declare(strict_types=1);

namespace Comave\Sales\Model\ResourceModel\Order;

use Umc\Crud\Model\ResourceModel\AbstractModel;

class Notification extends AbstractModel
{
    /**
     * Initialize resource model
     *
     * @return void
     * @codeCoverageIgnore
     * //phpcs:disable PSR2.Methods.MethodDeclaration.Underscore, PSR12.Methods.MethodDeclaration.Underscore
     */
    protected function _construct()
    {
        $this->_init('sales_order_notification_history', 'notification_id');
    }
    //phpcs: enable
}