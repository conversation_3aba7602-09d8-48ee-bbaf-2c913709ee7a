<?xml version="1.0"?>
<schema xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:Setup/Declaration/Schema/etc/schema.xsd">
    <table name="sales_order_notification_history" resource="default" engine="innodb"
           comment="Sales Order Notification History">
        <column xsi:type="int" name="notification_id" padding="10" unsigned="true" nullable="false" identity="true"
                comment="Notification ID"/>
        <column xsi:type="int" name="order_id" padding="10" unsigned="true" nullable="false" identity="false"
                comment="Order ID"/>
        <column xsi:type="text" name="message" nullable="true" comment="Notification Message"/>
        <column xsi:type="timestamp" name="created_at" on_update="false" nullable="false" default="CURRENT_TIMESTAMP"
                comment="Created at"/>
        <constraint xsi:type="primary" referenceId="PRIMARY">
            <column name="notification_id"/>
        </constraint>
        <constraint xsi:type="foreign"
                    referenceId="SALES_ORDER_NOTIFICATION_ID_SALES_ORDER_ENTITY_ID"
                    table="sales_order_notification_history" column="order_id"
                    referenceTable="sales_order" referenceColumn="entity_id"
                    onDelete="CASCADE"/>
        <index referenceId="SALES_ORDER_NOTIFICATION_HISTORY_ORDER_ID_INDEX" indexType="btree">
            <column name="order_id"/>
        </index>
    </table>
</schema>
