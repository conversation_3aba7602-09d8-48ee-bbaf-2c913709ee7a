<?php
/**
 * Copyright © Commercial Avenue
 * @var $block \Comave\Sales\Block\Adminhtml\Order\View\Tab\NotificationHistory
 */

$notificationHistory = $block->getNotificationHistory($block->getOrderId());
?>
<div class="edit-order-comments notification-history">
    <div class="edit-order-comments-block">
        <div class="edit-order-comments-block-title"><?= __('Notifications for this Order') ?></div>
        <?php foreach ($notificationHistory as $notification): ?>
            <div class="comments-block-item">
                <div class="comments-block-item-comment"><?= $notification->getMessage(); ?></div>
                <span class="comments-block-item-date-time">
                <?= $block->formatDate($notification->getCreatedAt(), \IntlDateFormatter::MEDIUM) ?>
                <?= $block->formatTime($notification->getCreatedAt(), \IntlDateFormatter::MEDIUM) ?>
            </span>
            </div>
        <?php endforeach; ?>
    </div>
</div>
