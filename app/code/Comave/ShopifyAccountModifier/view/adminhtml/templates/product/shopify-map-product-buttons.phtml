<?php
/** @var \Magento\Framework\View\Element\Template $block */
/** @var \Magento\Framework\Escaper $escaper */
/** @var \Comave\ShopifyAccountModifier\ViewModel\AccountHelper $accountHelperVm */
$accountHelperVm = $block->getData('accountViewModel');
/** @var \Magento\Framework\Model\AbstractModel $shopifyAccount */
$shopifyAccount = $accountHelperVm->getAccount();
$categoryMappingUrl = false;

if ($shopifyAccount->getId() && !empty($shopifyAccount->getSellerId())) {
    $categoryMappingUrl = $block->getUrl(
        'seller_onboarding/category_mapping/manual',
        [
            'seller' => $shopifyAccount->getSellerId()
        ]
    );
}
?>
<div class="page-main-actions">
    <div class='wk-product-import-buttons'>
        <button id="wk-import-product" title="<?=/* @noEscape */ __('Import Product(s) From Shopify'); ?>"
        type="button" class="button wk-import-product primary">
            <span class="button">
                <span><?=/* @noEscape */ __('Import Product(s) From Shopify'); ?></span>
            </span>
        </button>
        <?php if ($categoryMappingUrl): ?>
            <button id="wk_category_mapper"
                    title="<?=/* @noEscape */ __('Category Mapping'); ?>"
                    type="button"
                    class="button wk_category_mapper primary"
                    onclick="window.open('<?= $escaper->escapeUrl($categoryMappingUrl); ?>');">
                <span class="button">
                    <span><?=/* @noEscape */ __('Category Mapping'); ?></span>
                </span>
            </button>
        <?php endif; ?>
    </div>
</div>
