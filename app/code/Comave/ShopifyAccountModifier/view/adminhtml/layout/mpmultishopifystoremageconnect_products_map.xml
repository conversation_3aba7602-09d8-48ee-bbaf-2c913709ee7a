<?xml version="1.0"?>
<layout xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" layout="admin-2columns-left" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/layout_generic.xsd">
    <container name="content">
        <block name="MpMultiShopifyStoreMageConnect-product-mapping-area"
               template="Comave_ShopifyAccountModifier::product/shopify-map-product-buttons.phtml">
            <arguments>
                <argument xsi:type="object" name="accountViewModel">Comave\ShopifyAccountModifier\ViewModel\AccountHelper</argument>
            </arguments>
        </block>
    </container>
</layout>
