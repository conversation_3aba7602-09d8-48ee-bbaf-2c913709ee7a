<?php

declare(strict_types=1);

namespace Comave\ShopifyAccountModifier\ViewModel;

use Magento\Framework\App\RequestInterface;
use Magento\Framework\Model\AbstractModel;
use Magento\Framework\View\Element\Block\ArgumentInterface;
use Webkul\MpMultiShopifyStoreMageConnect\Api\Data\ShopifyaccountsInterface;
use Webkul\MpMultiShopifyStoreMageConnect\Api\Data\ShopifyaccountsInterfaceFactory;
use Webkul\MpMultiShopifyStoreMageConnect\Model\ResourceModel\Shopifyaccounts;

class AccountHelper implements ArgumentInterface
{
    /**
     * @param ShopifyaccountsInterfaceFactory $accountFactory
     * @param Shopifyaccounts $resourceModel
     * @param RequestInterface $request
     */
    public function __construct(
        private readonly ShopifyaccountsInterfaceFactory $accountFactory,
        private readonly Shopifyaccounts $resourceModel,
        private readonly RequestInterface $request
    ) {
    }

    /**
     * @return ShopifyaccountsInterface
     */
    public function getAccount(): ShopifyaccountsInterface
    {
        $model = $this->accountFactory->create();
        /** @var AbstractModel $model */
        $this->resourceModel->load(
            $model,
            (int) $this->request->getParam('id')
        );

        return $model;
    }
}
