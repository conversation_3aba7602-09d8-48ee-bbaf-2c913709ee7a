<?php

declare(strict_types=1);

namespace Comave\SplitOrder\Observer;

use Comave\SplitOrder\ViewModel\CommentVisibility;
use Magento\Framework\App\Area;
use Magento\Framework\App\State;
use Magento\Framework\Event\Observer;
use Magento\Framework\Event\ObserverInterface;
use Magento\Sales\Model\ResourceModel\Order\Status\History\Collection;
use Webkul\Marketplace\Helper\Data;

class FilterComments implements ObserverInterface
{
    /**
     * @param Data $mpHelper
     * @param State $appState
     */
    public function __construct(
        private readonly Data $mpHelper,
        private readonly State $appState
    ) {
    }

    /**
     * @param Observer $observer
     * @return void
     */
    public function execute(Observer $observer): void
    {
        $historyCollection = $observer->getData('order_status_history_collection');

        try {
            if (!$historyCollection instanceof Collection || $this->appState->getAreaCode() === Area::AREA_ADMINHTML) {
                return;
            }
        } catch (\Exception) {
            //Not really interested if app state is not set or emulated
            return;
        }

        $isSeller = $this->mpHelper->isSeller();

        if (!$isSeller) {
            $visibilities = CommentVisibility::getCustomerVisibilities();
        } else {
            $visibilities = array_merge(
                CommentVisibility::getSellerVisibilities(),
                [
                    $this->mpHelper->getCustomerId()
                ]
            );
        }

        $historyCollection->addFieldToFilter(
            'visibility',
            [
                'in' => $visibilities
            ]
        );
    }
}
