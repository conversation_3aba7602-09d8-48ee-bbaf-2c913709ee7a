<?xml version="1.0"?>
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" layout="admin-2columns-left" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <body>
        <referenceBlock name="order_history">
            <arguments>
                <argument xsi:type="object" name="visibilityViewModel">Comave\SplitOrder\ViewModel\CommentVisibility</argument>
            </arguments>
            <action method="setTemplate">
                <argument name="template" xsi:type="string">Comave_SplitOrder::order/view/history.phtml</argument>
            </action>
        </referenceBlock>
    </body>
</page>
