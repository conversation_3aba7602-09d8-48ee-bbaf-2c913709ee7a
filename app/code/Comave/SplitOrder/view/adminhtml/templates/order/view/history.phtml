<?php
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */

/** @var \Magento\Sales\Block\Adminhtml\Order\View\History $block */
/** @var \Magento\Framework\View\Helper\SecureHtmlRenderer $secureRenderer */
/** @var \Comave\SplitOrder\ViewModel\CommentVisibility $visibilityVm */
/** @var \Magento\Framework\Escaper $escaper */
$visibilityVm = $block->getData('visibilityViewModel');
$visibilityOptions = $visibilityVm->getHistoryCommentOptions();
?>
<div id="order_history_block" class="edit-order-comments">
    <?php if ($block->canAddComment()): ?>
        <div class="order-history-block" id="history_form">

            <div class="admin__field">
                <label for="history_status" class="admin__field-label"><?= $block->escapeHtml(__('Status')) ?></label>
                <div class="admin__field-control">
                    <select name="history[status]" id="history_status" class="admin__control-select">
                        <?php foreach ($block->getStatuses() as $_code => $_label): ?>
                            <option value="<?= $block->escapeHtmlAttr($_code) ?>"
                                <?php if ($_code == $block->getOrder()->getStatus()): ?> selected="selected"
                                <?php endif; ?>>
                                <?= $block->escapeHtml($_label) ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
            </div>

            <?php if (!empty($visibilityOptions)): ?>
                <div class="admin__field">
                    <label for="history_visibility" class="admin__field-label"><?= $block->escapeHtml(__('Comment Visibility')) ?></label>
                    <div class="admin__field-control">
                        <select name="history[visibility]" id="history_visibility" class="admin__control-select">
                            <?php foreach($visibilityOptions as $option): ?>
                                <option value="<?= $escaper->escapeHtml($option['value']); ?>">
                                    <?= $escaper->escapeHtml($option['label']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                </div>
            <?php endif; ?>

            <div class="admin__field">
                <label for="history_comment" class="admin__field-label">
                    <?= $block->escapeHtml(__('Comment')) ?>
                </label>
                <div class="admin__field-control">
                    <textarea name="history[comment]"
                              rows="3"
                              cols="5"
                              id="history_comment"
                              class="admin__control-textarea"></textarea>
                    <div class="admin__field-note">
                        <span>
                            <?= $block->escapeHtml(
                                __('A status change or comment text is required to submit a comment.')
                            )?>
                        </span>
                    </div>
                </div>
            </div>

            <div class="admin__field">
                <div class="order-history-comments-options">
                    <div class="admin__field admin__field-option">
                        <?php if ($block->canSendCommentEmail()): ?>
                            <input name="history[is_customer_notified]"
                                   type="checkbox"
                                   id="history_notify"
                                   class="admin__control-checkbox"
                                   value="1" />
                            <label class="admin__field-label" for="history_notify">
                                <?= $block->escapeHtml(__('Notify Customer by Email')) ?>
                            </label>
                        <?php endif; ?>
                    </div>

                    <div class="admin__field admin__field-option">
                        <input name="history[is_visible_on_front]"
                               type="checkbox"
                               id="history_visible"
                               class="admin__control-checkbox"
                               value="1" />
                        <label class="admin__field-label" for="history_visible">
                            <?= $block->escapeHtml(__('Visible on Storefront')) ?>
                        </label>
                    </div>
                </div>

                <div class="order-history-comments-actions">
                    <?= $block->getChildHtml('submit_button') ?>
                </div>
            </div>
        </div>
    <?php endif;?>

    <ul class="note-list">
    <?php foreach ($block->getOrder()->getStatusHistoryCollection(true) as $_item): ?>
        <li class="note-list-item">
            <span class="note-list-date">
                <?= /* @noEscape */ $block->formatDate($_item->getCreatedAt(), \IntlDateFormatter::MEDIUM) ?>
            </span>
            <span class="note-list-time">
                <?= /* @noEscape */ $block->formatTime($_item->getCreatedAt(), \IntlDateFormatter::MEDIUM) ?>
            </span>
            <span class="note-list-status"><?= $block->escapeHtml($_item->getStatusLabel()) ?></span>
            <span class="note-list-customer">
                <?= $block->escapeHtml(__('Customer')) ?>
                <?php if ($block->isCustomerNotificationNotApplicable($_item)): ?>
                    <span class="note-list-customer-notapplicable">
                        <?= $block->escapeHtml(__('Notification Not Applicable')) ?>
                    </span>
                <?php elseif ($_item->getIsCustomerNotified()): ?>
                    <span class="note-list-customer-notified"><?= $block->escapeHtml(__('Notified')) ?></span>
                <?php else: ?>
                    <span class="note-list-customer-not-notified"><?= $block->escapeHtml(__('Not Notified')) ?></span>
                <?php endif; ?>
            </span>
            <span class="note-list-status">
                <strong><?= __('Visibility:'); ?></strong>
                <?php $choseOption = array_search($_item->getVisibility(), array_column($visibilityOptions, 'value')); ?>
                <?= $block->escapeHtml($visibilityOptions[$choseOption]['label'] ?? 'N/A') ?>
            </span>
            <?php if ($_item->getComment()): ?>
                <div class="note-list-comment">
                    <?= $block->escapeHtml($_item->getComment(), ['b', 'br', 'strong', 'i', 'u', 'a']) ?>
                </div>
            <?php endif; ?>
        </li>
    <?php endforeach; ?>
    </ul>
    <?php $scriptString = <<<script
        require(['prototype'], function(){
            if($('order_status'))$('order_status').update('{$block->escapeJs($block->getOrder()->getStatusLabel())}');
        });
script;
    ?>
    <?= /* @noEscape */ $secureRenderer->renderTag('script', [], $scriptString, false) ?>
</div>
