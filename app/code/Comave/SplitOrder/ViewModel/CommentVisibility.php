<?php

declare(strict_types=1);

namespace Comave\SplitOrder\ViewModel;

use Comave\SellerApi\Service\OrderSellersIdentifier;
use Magento\Customer\Model\ResourceModel\Customer\CollectionFactory;
use Magento\Framework\DB\Select;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Registry;
use Magento\Framework\View\Element\Block\ArgumentInterface;
use Magento\Sales\Model\Order;
use Psr\Log\LoggerInterface;

class CommentVisibility implements ArgumentInterface
{
    /**
     * @param OrderSellersIdentifier $orderSellersIdentifier
     * @param CollectionFactory $collectionFactory
     * @param Registry $registry
     * @param LoggerInterface $logger
     */
    public function __construct(
        private readonly OrderSellersIdentifier $orderSellersIdentifier,
        private readonly CollectionFactory $collectionFactory,
        private readonly Registry $registry,
        private readonly LoggerInterface $logger
    ) {
    }

    /**
     * @return array[]
     * @throws LocalizedException
     */
    public function getHistoryCommentOptions(): array
    {
        /** @var Order $currentOrder */
        $currentOrder = $this->registry->registry('current_order');
        $sellerItems = $this->orderSellersIdentifier->get($currentOrder, true);

        if (empty($sellerItems)) {
            $this->logger->warning(
                '[ComaveSellerHistory] Unable to determine seller items',
                [
                    'order' => $currentOrder->getIncrementId()
                ]
            );

            return [];
        }

        $options = [
            [
                'value' => 0,
                'label' => __('General')
            ],
            [
                'value' => 1,
                'label' => __('Only Admin')
            ],
            [
                'value' => 2,
                'label' => __('All Sellers')
            ],

        ];

        $sellerCollection = $this->collectionFactory->create();
        $sellerCollection->getSelect()
            ->reset(Select::COLUMNS)
            ->columns([
                'firstname',
                'lastname',
                'entity_id'
            ])
        ;
        $sellerCollection->addFieldToFilter(
            'entity_id',
            [
                'in' => array_keys($sellerItems)
            ]
        );

        foreach ($sellerCollection->getItems() as $customer) {
            $options[] = [
                'value' => $customer->getId(),
                'label' => sprintf(
                    'Seller: %s %s (%s)',
                    $customer->getFirstname(),
                    $customer->getLastname(),
                    $customer->getId()
                ),
            ];
        }

        return $options;
    }

    /**
     * @return int[]
     */
    public static function getCustomerVisibilities(): array
    {
        return [0];
    }

    /**
     * @return int[]
     */
    public static function getSellerVisibilities(): array
    {
        return [0, 2];
    }
}
