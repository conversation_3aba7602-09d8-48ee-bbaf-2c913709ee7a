<?php

declare(strict_types=1);

namespace Comave\SplitOrder\Plugin\Adminhtml;

use Magento\Framework\App\RequestInterface;

class SetCommentVisibility
{
    /**
     * @param RequestInterface $request
     */
    public function __construct(private readonly RequestInterface $request)
    {
    }

    /**
     * @param \Magento\Sales\Api\Data\OrderStatusHistoryInterface $orderStatusHistory
     * @return void
     */
    public function beforeSave(
        \Magento\Sales\Api\Data\OrderStatusHistoryInterface $orderStatusHistory
    ): void {
        $requestData = $this->request->getParam('history');
        $visibility = (int) $requestData['visibility'];
        $orderStatusHistory->setData('visibility', $visibility);
    }
}
