<?php
declare(strict_types=1);

namespace Comave\Marketplace\Plugin;

use Magento\Framework\App\ActionInterface;
use Magento\Customer\Model\Session as CustomerSession;
use Magento\Framework\Controller\ResultFactory;
use Magento\Framework\Message\ManagerInterface as MessageManager;
use Magento\Framework\UrlInterface;
use Psr\Log\LoggerInterface;
use Comave\Marketplace\Service\AdminImpersonation;

class BlockSellerStripeActions
{
    private const ERROR_MESSAGE = 'Stripe settings are locked while you are in Admin Mode.';

    private const SELLER_PAYOUTS_PATH = 'seller_payouts/seller/payouts';

    public function __construct(
        private readonly AdminImpersonation $adminImpersonation,
        private readonly CustomerSession $customerSession,
        private readonly ResultFactory   $resultFactory,
        private readonly MessageManager  $messageManager,
        private readonly UrlInterface    $url,
        private readonly LoggerInterface $logger
    ) {}

    public function aroundExecute(ActionInterface $subject, callable $proceed)
    {
        if (!$this->adminImpersonation->isAdminImpersonating()) {
            return $proceed();
        }

        $this->messageManager->addErrorMessage(__(self::ERROR_MESSAGE));

        $this->logger->warning('[StripeImpersonationGuard] Blocked impersonated attempt to modify Stripe settings.', [
            'controller' => get_class($subject),
            'admin_user_id' => $this->customerSession->getData(AdminImpersonation::CORE_IMPERSONATION_KEY),
            'customer_id' => $this->customerSession->getCustomerId(),
        ]);

        $result = $this->resultFactory->create(ResultFactory::TYPE_REDIRECT);
        $result->setUrl($this->url->getUrl(self::SELLER_PAYOUTS_PATH));
        return $result;
    }
}
