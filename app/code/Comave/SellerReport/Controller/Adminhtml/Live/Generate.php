<?php
declare(strict_types=1);

namespace Comave\SellerReport\Controller\Adminhtml\Live;

use Magento\Backend\App\Action;
use Magento\Backend\App\Action\Context;
use Comave\SellerReport\Cron\GenerateDailyReport;
use Comave\SellerReport\Model\Config\Source\CountryOptions;

class Generate extends Action
{

    const ADMIN_RESOURCE = 'Comave_SellerReport::seller_report';

    /**
     * @param Context $context
     * @param GenerateDailyReport $reportGenerator
     * @param CountryOptions $countryOptions
     */
    public function __construct(
        Context $context,
        private GenerateDailyReport $reportGenerator,
        private readonly CountryOptions $countryOptions
    ) {
        parent::__construct($context);
    }

    /**
     * Execute the report generation
     *
     * @return \Magento\Framework\Controller\ResultInterface
     */
    public function execute()
    {
        try {
            $result = $this->reportGenerator->execute();
            
            if ($result) {
                $this->countryOptions->clearCache();
                $this->messageManager->addSuccessMessage(__('The seller report has been successfully generated.'));
            } else {
                $this->messageManager->addWarningMessage(__('The report was processed but no data was generated.'));
            }
        } catch (\Exception $e) {
            $this->messageManager->addErrorMessage(__('An error occurred while generating the report: %1', $e->getMessage()));
        }
        
        $resultRedirect = $this->resultRedirectFactory->create();
        return $resultRedirect->setPath('comave_report/live/sellers');
    }
}
