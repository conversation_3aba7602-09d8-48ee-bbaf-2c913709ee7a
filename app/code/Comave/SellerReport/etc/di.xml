<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" 
        xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <type name="Magento\Framework\View\Element\UiComponent\DataProvider\CollectionFactory">
        <arguments>
            <argument name="collections" xsi:type="array">
                <item name="comave_seller_report_listing_data_source" xsi:type="string">Comave\SellerReport\Model\ResourceModel\SellerReport\Grid\Collection</item>
            </argument>
        </arguments>
    </type>
    <virtualType name="Comave\SellerReport\Model\ResourceModel\SellerReport\Grid\Collection" type="Magento\Framework\View\Element\UiComponent\DataProvider\SearchResult">
        <arguments>
            <argument name="mainTable" xsi:type="string">comave_seller_report</argument>
            <argument name="resourceModel" xsi:type="string">Comave\SellerReport\Model\ResourceModel\SellerReport</argument>
        </arguments>
    </virtualType>
    <type name="Comave\SellerReport\Cron\GenerateDailyReport">
        <arguments>
            <argument name="logger" xsi:type="object">Psr\Log\LoggerInterface</argument>
        </arguments>
    </type>
</config>