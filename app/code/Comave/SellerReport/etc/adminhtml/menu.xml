<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" 
        xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Backend:etc/menu.xsd">
    <menu>
        <add id="Comave_SellerReport::seller_report" 
             title="Seller Reports" 
             module="Comave_SellerReport" 
             sortOrder="50" 
             parent="Magento_Reports::report" 
             resource="Comave_SellerReport::seller_report"/>

        <add id="Comave_SellerReport::sellers_by_country" 
             title="Sellers by Country" 
             module="Comave_SellerReport" 
             sortOrder="10" 
             parent="Comave_SellerReport::seller_report" 
             action="comave_report/live/sellers" 
             resource="Comave_SellerReport::sellers_by_country"/>
    </menu>
</config>