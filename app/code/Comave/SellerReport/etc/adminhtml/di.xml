<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" 
        xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <type name="Comave\SellerReport\Model\ResourceModel\SellerReport\Collection">
        <arguments>
            <argument name="eavConfig" xsi:type="object">Magento\Eav\Model\Config</argument>
        </arguments>
    </type>
    <type name="Comave\SellerReport\Cron\GenerateDailyReport">
        <arguments>
            <argument name="logger" xsi:type="object">Psr\Log\LoggerInterface</argument>
        </arguments>
    </type>
</config>
