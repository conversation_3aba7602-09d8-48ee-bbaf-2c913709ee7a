<?xml version="1.0"?>
<schema xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:Setup/Declaration/Schema/etc/schema.xsd">
    <table name="comave_seller_report" resource="default" engine="innodb" comment="Comave Seller Country Snapshot Report">
        <column xsi:type="int" name="report_id" unsigned="true" nullable="false" identity="true" comment="Report ID"/>
        <column xsi:type="date" name="report_date" nullable="false" comment="Report Snapshot Date"/>
        <column xsi:type="varchar" name="country" length="20" nullable="false" comment="Country Code (ISO)"/>
        <column xsi:type="int" name="live_sellers" unsigned="true" nullable="false" default="0" comment="Live Sellers Count"/>
        <column xsi:type="int" name="not_live_sellers" unsigned="true" nullable="false" default="0" comment="Not Live Sellers Count"/>
        <column xsi:type="timestamp" name="created_at" nullable="false" default="CURRENT_TIMESTAMP" on_update="false" comment="Created At"/>
        <column xsi:type="timestamp" name="updated_at" nullable="false" default="CURRENT_TIMESTAMP" on_update="true" comment="Updated At"/>
        <constraint xsi:type="primary" referenceId="PRIMARY">
            <column name="report_id"/>
        </constraint>
        <constraint xsi:type="unique" referenceId="COMAVE_REPORT_DATE_COUNTRY_UNIQ">
            <column name="report_date"/>
            <column name="country"/>
        </constraint>
        <index referenceId="COMAVE_SELLER_REPORT_COUNTRY_IDX" indexType="btree">
            <column name="country"/>
        </index>
    </table>
</schema>
