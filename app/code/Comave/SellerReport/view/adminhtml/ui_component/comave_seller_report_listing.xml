<?xml version="1.0"?>
<listing xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Ui:etc/ui_configuration.xsd"
         name="comave_seller_report_listing">
    <argument name="data" xsi:type="array">
        <item name="js_config" xsi:type="array">
            <item name="provider" xsi:type="string">comave_seller_report_listing.comave_seller_report_listing_data_source</item>
            <item name="deps" xsi:type="string">comave_seller_report_listing.comave_seller_report_listing_data_source</item>
        </item>
        <item name="spinner" xsi:type="string">comave_seller_report_listing_columns</item>
        <item name="buttons" xsi:type="array">
            <item name="add" xsi:type="array">
                <item name="name" xsi:type="string">generate_report</item>
                <item name="label" xsi:type="string" translate="true">Generate Today's Report</item>
                <item name="class" xsi:type="string">primary</item>
                <item name="url" xsi:type="string">*/*/generate</item>
            </item>
        </item>
    </argument>
    
    <dataSource name="comave_seller_report_listing_data_source">
        <argument name="dataProvider" xsi:type="configurableObject">
            <argument name="class" xsi:type="string">Magento\Framework\View\Element\UiComponent\DataProvider\DataProvider</argument>
            <argument name="name" xsi:type="string">comave_seller_report_listing_data_source</argument>
            <argument name="primaryFieldName" xsi:type="string">report_id</argument>
            <argument name="requestFieldName" xsi:type="string">report_id</argument>
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="component" xsi:type="string">Magento_Ui/js/grid/provider</item>
                    <item name="update_url" xsi:type="url" path="mui/index/render"/>
                    <item name="storageConfig" xsi:type="array">
                        <item name="indexField" xsi:type="string">report_id</item>
                    </item>
                </item>
            </argument>
        </argument>
        <settings>
            <updateUrl path="mui/index/render"/>
        </settings>
    </dataSource>

    <listingToolbar name="listing_top">
        <settings>
            <sticky>true</sticky>
        </settings>
        <bookmark name="bookmarks"/>
        <columnsControls name="columns_controls"/>
        <filters name="listing_filters">
            <settings>
                <templates>
                    <filters>
                        <select>
                            <param name="template" xsi:type="string">ui/grid/filters/elements/ui-select</param>
                            <param name="component" xsi:type="string">Magento_Ui/js/form/element/ui-select</param>
                        </select>
                    </filters>
                </templates>
            </settings>
        </filters>
        <paging name="listing_paging"/>
        <exportButton name="export_button">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="selectProvider" xsi:type="string">comave_seller_report_listing.comave_seller_report_listing.comave_seller_report_listing_columns.ids</item>
                    <item name="options" xsi:type="array">
                        <item name="csv" xsi:type="array">
                            <item name="value" xsi:type="string">csv</item>
                            <item name="label" xsi:type="string" translate="true">CSV</item>
                            <item name="url" xsi:type="string">mui/export/gridToCsv</item>
                        </item>
                    </item>
                </item>
            </argument>
        </exportButton>
    </listingToolbar>

    <columns name="comave_seller_report_listing_columns">
        <settings>
            <editorConfig>
                <param name="selectProvider" xsi:type="string">comave_seller_report_listing.comave_seller_report_listing.comave_seller_report_listing_columns.ids</param>
                <param name="enabled" xsi:type="boolean">false</param>
                <param name="indexField" xsi:type="string">report_id</param>
                <param name="clientConfig" xsi:type="array">
                    <item name="saveUrl" xsi:type="url" path="*/*/inlineEdit"/>
                    <item name="validateBeforeSave" xsi:type="boolean">false</item>
                </param>
            </editorConfig>
            <childDefaults>
                <param name="fieldAction" xsi:type="array">
                    <item name="provider" xsi:type="string">comave_seller_report_listing.comave_seller_report_listing.comave_seller_report_listing_columns</item>
                    <item name="target" xsi:type="string">startEdit</item>
                    <item name="params" xsi:type="array">
                        <item name="0" xsi:type="string">${ $.$data.rowIndex }</item>
                        <item name="1" xsi:type="boolean">true</item>
                    </item>
                </param>
            </childDefaults>                
        </settings>
        <selectionsColumn name="ids">
            <settings>
                <indexField>report_id</indexField>
                <resizeEnabled>false</resizeEnabled>
                <resizeDefaultWidth>55</resizeDefaultWidth>
            </settings>
        </selectionsColumn>
        <column name="section_header" class="Magento\Ui\Component\Listing\Columns\Column" sortOrder="5">
            <settings>
                <label translate="true">Comave Report</label>
                <sortable>false</sortable>
                <filter>false</filter>
                <bodyTmpl>ui/grid/cells/html</bodyTmpl>
                <visible>false</visible>
            </settings>
        </column>

        <column name="report_date" sortOrder="10">
            <settings>
                <label translate="true">Report Date</label>
                <filter>dateRange</filter>
                <dataType>date</dataType>
                <dateFormat>yyyy-MM-dd</dateFormat>
                <sortable>true</sortable>
            </settings>
        </column>

        <column name="country" sortOrder="20">
            <settings>
                <label translate="true">Country</label>
                <filter>select</filter>
                <options class="Comave\SellerReport\Model\Config\Source\CountryOptions"/>
                <dataType>select</dataType>
                <sortable>true</sortable>
            </settings>
        </column>

        <column name="live_sellers" sortOrder="30">
            <settings>
                <label translate="true">Live Sellers</label>
                <filter>false</filter>
                <dataType>number</dataType>
                <sortable>true</sortable>
            </settings>
        </column>

        <column name="not_live_sellers" sortOrder="40">
            <settings>
                <label translate="true">Not Live Sellers</label>
                <filter>false</filter>
                <dataType>number</dataType>
                <sortable>true</sortable>
            </settings>
        </column>
    </columns>
</listing>
