<?php
declare(strict_types=1);

namespace Comave\SellerReport\Model\ResourceModel\SellerReport;

use Magento\Framework\Model\ResourceModel\Db\Collection\AbstractCollection;
use Comave\SellerReport\Model\SellerReport as SellerReportModel;
use Comave\SellerReport\Model\ResourceModel\SellerReport as SellerReportResource;
use Comave\SellerReport\Enum\SellerStatus;

class Collection extends AbstractCollection
{
    /**
     * Define model and resource model
     */
    protected function _construct(): void
    {
        $this->_init(SellerReportModel::class, SellerReportResource::class);
    }

    /**
     * Handle field filtering
     *
     * @param string $field
     * @param mixed $condition
     * @return $this
     */
    public function addFieldToFilter($field, $condition = null): static
    {
        if ($field === 'report_date' || $field === 'country') {
            return parent::addFieldToFilter($field, $condition);
        }

        if (SellerStatus::tryFromField($field) !== null) {
            return $this;
        }

        return parent::addFieldToFilter($field, $condition);
    }
}