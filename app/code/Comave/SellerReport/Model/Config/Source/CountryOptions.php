<?php
declare(strict_types=1);

namespace Comave\SellerReport\Model\Config\Source;

use Magento\Framework\App\ResourceConnection;
use Magento\Framework\Data\OptionSourceInterface;
use Magento\Directory\Model\ResourceModel\Country\CollectionFactory as CountryCollectionFactory;
use Magento\Framework\App\CacheInterface;
use Magento\Framework\Serialize\SerializerInterface;

class CountryOptions implements OptionSourceInterface
{
    private const CACHE_KEY = 'comave_seller_report_country_options';
    private const CACHE_LIFETIME = 3600; // 1 hour in seconds
    private const UNKNOWN_COUNTRY = 'UNKNOWN';
    
    /**
     * @var array|null
     */
    private $options;

    /**
     * @param ResourceConnection $resourceConnection
     * @param CountryCollectionFactory $countryCollectionFactory
     * @param CacheInterface $cache
     * @param SerializerInterface $serializer
     */
    public function __construct(
        private readonly ResourceConnection $resourceConnection,
        private readonly CountryCollectionFactory $countryCollectionFactory,
        private readonly CacheInterface $cache,
        private readonly SerializerInterface $serializer
    ) {}

    /**
     * Get options
     *
     * @return array
     */
    public function toOptionArray(): array
    {
        if ($this->options === null) {
            $cacheData = $this->cache->load(self::CACHE_KEY);
            
            if ($cacheData) {
                $this->options = $this->serializer->unserialize($cacheData);
            } else {
                $this->options = $this->loadOptionsFromDb();
                $this->cache->save(
                    $this->serializer->serialize($this->options),
                    self::CACHE_KEY,
                    ['COMAVE_REPORTS_CACHE'],
                    self::CACHE_LIFETIME
                );
            }
        }
        
        return $this->options;
    }
    
    /**
     * Load options from database
     * 
     * @return array
     */
    private function loadOptionsFromDb(): array
    {
        $connection = $this->resourceConnection->getConnection();
        $tableName = $this->resourceConnection->getTableName('comave_seller_report');
        
        $select = $connection->select()
            ->from($tableName, ['country'])
            ->distinct(true);
        
        $countryCodes = $connection->fetchCol($select);

        $countryOptions = [];
        
        if (!empty($countryCodes)) {
            $countryCollection = $this->countryCollectionFactory->create()
                ->loadByStore();
            $countryCollection->addFieldToFilter('country_id', ['in' => $countryCodes]);
            
            foreach ($countryCollection as $country) {
                $countryOptions[] = [
                    'value' => $country->getCountryId(),
                    'label' => $country->getName()
                ];
            }

            $existingCodes = array_flip(array_column($countryOptions, 'value'));
            foreach ($countryCodes as $code) {
                if ($code === self::UNKNOWN_COUNTRY) {
                    $countryOptions[] = [
                        'value' => self::UNKNOWN_COUNTRY,
                        'label' => __('Unknown Country')
                    ];
                } elseif (!isset($existingCodes[$code])) {
                    $countryOptions[] = [
                        'value' => $code,
                        'label' => $code
                    ];
                }
            }
        }

        usort($countryOptions, function ($a, $b) {
            $labelA = (string)$a['label'];
            $labelB = (string)$b['label'];
            return strcmp($labelA, $labelB);
        });
        
        return $countryOptions;
    }
    
    /**
     * Clear the country options cache
     * 
     * @return void
     */
    public function clearCache(): void
    {
        $this->cache->remove(self::CACHE_KEY);
        $this->options = null;
    }
}
