<?php
declare(strict_types=1);

namespace Comave\SellerReport\Cron;

use Magento\Framework\App\ResourceConnection;
use Psr\Log\LoggerInterface;
use Webkul\Marketplace\Model\ResourceModel\Seller\CollectionFactory as SellerCollectionFactory;
use Webkul\Marketplace\Model\ResourceModel\Product\CollectionFactory as ProductCollectionFactory;
use Comave\SellerReport\Model\Config\Source\CountryOptions;

class GenerateDailyReport
{
    /**
     * @param ResourceConnection $resourceConnection
     * @param LoggerInterface $logger
     * @param SellerCollectionFactory $sellerCollectionFactory
     * @param ProductCollectionFactory $productCollectionFactory
     * @param CountryOptions $countryOptions
     */
    public function __construct(
        private readonly ResourceConnection $resourceConnection,
        private readonly LoggerInterface $logger,
        private readonly SellerCollectionFactory $sellerCollectionFactory,
        private readonly ProductCollectionFactory $productCollectionFactory,
        private readonly CountryOptions $countryOptions
    ) {}

    /**
     * Generate daily seller report
     *
     * @return bool
     */
    public function execute(): bool
    {
        try {
            $today = date('Y-m-d');
            $connection = $this->resourceConnection->getConnection();
            $tableName = $this->resourceConnection->getTableName('comave_seller_report');

            $sellersByCountry = $this->getSellersByCountry();
            if (empty($sellersByCountry)) {
                $this->logger->warning('No seller data found to insert');
                return false;
            }

            $this->logger->info('Collected data for ' . count($sellersByCountry) . ' countries');

            $connection->beginTransaction();
            try {
                $connection->delete($tableName, ['report_date = ?' => $today]);

                foreach ($sellersByCountry as $country => $data) {
                    $insertData = [
                        'report_date' => $today,
                        'country' => $country,
                        'live_sellers' => $data['live'] ?? 0,
                        'not_live_sellers' => $data['not_live'] ?? 0,
                        'created_at' => date('Y-m-d H:i:s')
                    ];
                    $connection->insert($tableName, $insertData);
                }

                $connection->commit();

                $this->countryOptions->clearCache();
                $this->logger->info('Country options cache cleared after report generation');
                
                return true;
            } catch (\Exception $e) {
                $connection->rollBack();
                throw $e;
            }
        } catch (\Exception $e) {
            $this->logger->critical('Error generating seller report: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString()
            ]);
            return false;
        }
    }

    /**
     * Get count of sellers by country, split by live status
     *
     * @return array
     */
    private function getSellersByCountry(): array
    {
        $sellersByCountry = [];

        try {
            $productCollection = $this->productCollectionFactory->create();
            $sellerIdsWithProducts = array_unique(
                array_map('intval', $productCollection->getColumnValues('seller_id'))
            );

            $sellerCollection = $this->sellerCollectionFactory->create();
            $sellerCollection->addFieldToFilter('is_seller', 1);
            $sellerCollection->addFieldToSelect(['seller_id', 'country_pic']);

            foreach ($sellerCollection as $seller) {
                $sellerId = (int) $seller->getData('seller_id');
                $country = $seller->getData('country_pic') ?: 'UNKNOWN';

                if (!isset($sellersByCountry[$country])) {
                    $sellersByCountry[$country] = ['live' => 0, 'not_live' => 0];
                }

                if (in_array($sellerId, $sellerIdsWithProducts)) {
                    $sellersByCountry[$country]['live']++;
                } else {
                    $sellersByCountry[$country]['not_live']++;
                }
            }
        } catch (\Exception $e) {
            $this->logger->critical('Error collecting seller data: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString()
            ]);
        }

        return $sellersByCountry;
    }

}
