<?php
declare(strict_types=1);

namespace Comave\SellerPayouts\Block;

use Comave\Marketplace\Service\AdminImpersonation;
use Magento\Framework\View\Element\Template;
use Magento\Framework\View\Element\Template\Context;

class StripeButton extends Template
{
    public function __construct(
        Context $context,
        private readonly AdminImpersonation $adminImpersonation,
        array $data = []
    ) {
        parent::__construct($context, $data);
    }

    /**
     * Check if admin is impersonating a customer
     *
     * @return bool
     */
    public function isAdminImpersonating(): bool
    {
        return $this->adminImpersonation->isAdminImpersonating();
    }

    /**
     * Get button label
     *
     * @return string
     */
    public function getLabel(): string
    {
        return (string) $this->getData('label');
    }

    /**
     * Get button URL
     *
     * @return string
     */
    public function getButtonUrl(): string
    {
        return (string) $this->getData('url');
    }

    /**
     * Get button type
     *
     * @return string
     */
    public function getType(): string
    {
        return $this->getData('type') ?: 'button';
    }

    /**
     * Get CSS class
     *
     * @return string
     */
    public function getCssClass(): string
    {
        return (string) $this->getData('css_class');
    }
}
