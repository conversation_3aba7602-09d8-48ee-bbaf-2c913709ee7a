<?php
/**
 * Webkul Software.
 *
 * @category  Webkul
 * @package   Webkul_Marketplace
 * <AUTHOR>
 * @copyright Copyright (c) Webkul Software Private Limited (https://webkul.com)
 * @license   https://store.webkul.com/license.html
 */
namespace Webkul\Marketplace\Ui\DataProvider;

use Webkul\Marketplace\Model\ResourceModel\Saleslist\CollectionFactory;
use Webkul\Marketplace\Model\ResourceModel\Saleslist\Collection as OrderColl;
use Webkul\Marketplace\Helper\Data as HelperData;
use Magento\Framework\DB\Sql\ExpressionFactory;
use Comave\MaskedEmail\Service\SellerContract;

class CustomerHistoryDataProvider extends \Magento\Ui\DataProvider\AbstractDataProvider
{
    /**
     * Collection for getting table name
     *
     * @var \Webkul\Marketplace\Model\ResourceModel\Saleslist\Collection
     */
    protected $orderColl;

    /**
     * Saleslist Orders collection
     *
     * @var \Webkul\Marketplace\Model\ResourceModel\Saleslist\Collection
     */
    protected $collection;

    /**
     * @var HelperData
     */
    public $helperData;

    /**
     * @var \Magento\Framework\DB\Sql\ExpressionFactory
     */
    private $expressionFactory;
    /**
     * @var \Comave\MaskedEmail\Service\SellerContract
     */
    private $sellerContract;

    public function __construct(
        $name,
        $primaryFieldName,
        $requestFieldName,
        OrderColl $orderColl,
        CollectionFactory $collectionFactory,
        HelperData $helperData,
        ExpressionFactory $expressionFactory,
        SellerContract $sellerContract,
        array $meta = [],
        array $data = []
    ) {
        $this->expressionFactory = $expressionFactory;
        $this->sellerContract = $sellerContract;
        parent::__construct($name, $primaryFieldName, $requestFieldName, $meta, $data);
        $sellerId = $helperData->getCustomerId();

        $customerGridFlat = $orderColl->getTable('customer_grid_flat');
        $collectionData = $collectionFactory->create()
            ->addFieldToFilter('seller_id', $sellerId);

        $collectionData->getSelect()
            ->columns('SUM(actual_seller_amount) AS customer_base_total')
            ->columns('count(distinct(order_id)) AS order_count')
            ->group('magebuyer_id');

        $collectionData->getSelect()->join(
            $customerGridFlat.' as cgf',
            'main_table.magebuyer_id = cgf.entity_id',
            [
                'name' => 'name',
                'masked_email' => 'masked_email',
                'billing_telephone' => 'billing_telephone',
                'gender' => 'gender',
                'billing_full' => 'billing_full'
            ]
        );

        $isPremiumSeller = $this->sellerContract->isPremiumSeller((int)$sellerId);
        $sellerHasClub = $this->sellerContract->hasAssignedClub((int)$sellerId);
        if ($isPremiumSeller || $sellerHasClub) {
            $emailExpr = 'cgf.email';
        } else {
            $maskedEmail = \Comave\MaskedEmail\Service\MaskedEmailGenerator::generateMaskedHash('');
            $emailExpr = $this->expressionFactory->create([
                'expression' => "
                    IF(
                        cgf.masked_email IS NULL OR cgf.masked_email = '',
                        CONCAT('',  '$maskedEmail'),
                        cgf.masked_email
                    )
                "
            ]);
        }

        $collectionData->getSelect()->columns([
            'email' => $emailExpr
        ]);

        $this->collection = $collectionData;
    }
}
