<?php
/**
 * Webkul Software.
 *
 * @category  Webkul
 * @package   Webkul_Marketplace
 * <AUTHOR>
 * @copyright Copyright (c) Webkul Software Private Limited (https://webkul.com)
 * @license   https://store.webkul.com/license.html
 */
/** @var $block \Webkul\Marketplace\Block\Page\Header */
/** @var \Magento\Framework\Escaper $escaper */
$helper = $block->getHelper();
?>

<?php  if ($block->getShowPart() == "logo") {?>
        <a
            href="<?= $escaper->escapeUrl($block->getUrl('')) ?>"
            class="wk-mp-logo">
            <span class="wk-mp-logo-img-wrapper">
                <img class="wk-mp-logo-img" src="<?= $escaper
                ->escapeUrl($block->getSellerDashboardLogoSrc())?>"
                alt="<?= $escaper->escapeHtml(__('Seller Panel')) ?>"/>
            </span>
        </a>
<?php } ?>

<?php  if ($block->getShowPart() == "user") {?>
    <?php
        $logo = $block->getSellerLogo();
        $logo = $helper->getMediaUrl().'avatar/'.$logo;
    ?>
        <div class="wk-mp-user wk-mp__action-dropdown-wrap">
            <a
                href="#"
                onlick="return false;"
                class="wk-mp__action-dropdown"
                title="<?= $escaper->escapeHtml(__('My Account')) ?>"
                data-mage-init='{"dropdown":{}}'
                data-toggle="dropdown">
                <span class="wk-mp-seller-header-logo-wrapper">
                    <img class="wk-mp-seller-header-logo" src="<?= $escaper->escapeUrl($logo)?>"
                     alt="<?= $escaper->escapeHtml(__('Seller Panel')) ?>"/>
                </span>
                <span class="wk-mp__action-dropdown-text">
                    <span class="wk-mp-user-account-text">
                        <span class="wk-mp-user-account-text-wrapper">
                            <?= $escaper->escapeHtml($block->getSellerShopName()); ?>
                        </span>
                    </span>
                </span>
            </a>
            <ul class="wk-mp__action-dropdown-menu">
                <li class="wk-mp-dropdown-menu-settings">
                    <a
                        href="<?= /* @escapeNotVerified */ $escaper->escapeUrl($block
                        ->getUrl('marketplace/account/editprofile')) ?>"
                        <?= /* @escapeNotVerified */ $escaper->escapeHtml($block
                        ->getUiId('user', 'account', 'settings'))?>
                        title="<?= $escaper->escapeHtml(__('Account Setting')) ?>">
                        <?= /* @escapeNotVerified */ $escaper->escapeHtml(__('Account Setting')) ?>
                    </a>
                </li>
                <li class="wk-mp-dropdown-menu-buyer">
                    <a
                        href="https://comave.com"
                        title="<?= $escaper->escapeHtml(__('Buyer Dashboard')); ?>"
                        target="_blank" class="store-front">
                        <?= /* @escapeNotVerified */ $escaper->escapeHtml(__('Buyer Dashboard')); ?>
                    </a>
                </li>
                <li class="wk-mp-dropdown-menu-logout">
                    <a
                        href="<?= /* @escapeNotVerified */ $escaper->escapeUrl($block
                        ->getUrl('customer/account/logout')) ?>"
                        class="wk-mp-account-logout"
                        title="<?= $escaper->escapeHtml(__('Logout')) ?>">
                        <?= /* @escapeNotVerified */ $escaper->escapeHtml(__('Logout')) ?>
                    </a>
                </li>
            </ul>
        </div>
<?php } ?>

<?php if ($block->getShowPart() == "seller.notifications") {?>
    <?php $totalCount = $block->getAllNotificationCount(); ?>
        <div class="wk-mp-notification wk-mp__action-dropdown-wrap">
            <a
                onlick="return false;"
                href="#"
                class="wk-mp__action-dropdown"
                <?php if ($totalCount): ?> data-mage-init='{"dropdown":{}}' <?php endif; ?> >
                <span class="wk-mp-notification-icon"></span>
                <?php if ($totalCount): ?>
                    <?php if ($totalCount > 5): ?>
                        <span class="wk-mp-notification-count">5+</span>
                    <?php else: ?>
                        <span class="wk-mp-notification-count">
                            <?= $escaper->escapeHtml($totalCount); ?>
                        </span>
                    <?php endif; ?>
                <?php endif; ?>
            </a>
            <?php if ($totalCount): ?>
                <ul class="wk-mp__action-dropdown-menu">
                    <?php foreach ($block->getAllNotification() as $notification) {
                        /* @noEscape */ echo $block->getNotificationInfo($notification);
                    } ?>
                    <li>
                        <small>
                            <a href="<?= $escaper->escapeUrl($block
                            ->getUrl('marketplace/account/notification'))?>">
                                <?= $escaper->escapeHtml(__("View All"))?>
                            </a>
                        </small>
                    </li>
                </ul>
            <?php endif; ?>
        </div>
<?php } ?>

<?php if ($block->getShowPart() == "seller.home") {?>
    <div class="wk-mp-home wk-mp__action-dropdown-wrap">
        <a
            href="<?= $escaper->escapeUrl($block->getUrl('marketplace/account/dashboard')) ?>"
            class="wk-mp__action-dropdown">
        </a>
    </div>
<?php } ?>

<?php if ($block->getShowPart() == "seller.assistance") { ?>
    <div class="wk-mp-assistance wk-mp__action-dropdown-wrap">
        <a  href="https://comavepartners.zendesk.com/hc/en-us"
            target="_blank"
            class="wk-mp__action-dropdown"
            title="<?= $escaper->escapeHtml(__('Seller Assistance')) ?>">
        </a>
    </div>
<?php } ?>

<?php if ($block->getShowPart() == "other") {?>
    <?= $block->getChildHtml(); ?>
<?php } ?>
