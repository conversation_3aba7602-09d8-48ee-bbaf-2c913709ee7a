<?xml version="1.0"?>
<!--
/**
 * Webkul Software.
 *
 * @category  Webkul
 * @package   Webkul_Marketplace
 * <AUTHOR>
 * @copyright Copyright (c) Webkul Software Private Limited (https://webkul.com)
 * @license   https://store.webkul.com/license.html
 */
-->
<layout xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_layout.xsd">
    <container name="root">
        <container name="seller.menu.wrapper" before="-" htmlTag="div" htmlClass="wk-mp-menu-wrapper"/>
        <container name="after.body.start" as="after.body.start" before="-" label="Page Top"/>
        <container name="seller.page.wrapper" as="seller_page_wrapper" htmlTag="div" htmlClass="wk-mp-page-wrapper">
            <container name="seller.global.notices" as="seller_global_notices" before="-"/>
            <container name="seller.header.before.container" before="-" htmlTag="div" htmlClass="wk-mp-page-header-before page-header"/>
            <container name="seller.header" after="seller.notifications" htmlTag="header" htmlClass="wk-mp-page-header"/>
            <container name="seller.page.menu" as="seller.page.menu" after="header"/>
            <container name="seller.main.content" htmlTag="main" htmlId="wk-mp-maincontent" htmlClass="wk-mp-page-main">
                <container name="seller.columns.top" label="Before Main Columns"/>
                <container name="seller.columns" htmlTag="div" htmlClass="wk-mp-columns">
                    <container name="seller.main" label="Main Content Container" htmlTag="div" htmlClass="wk-mp-column wk-mp-main"/>
                </container>
            </container>
            <container name="seller.page.bottom.container" as="seller_page_bottom_container" label="Before Page Footer Container" after="seller.main.content" htmlTag="div" htmlClass="wk-mp-page-bottom"/>
            <container name="before.body.end" as="before_body_end" after="-" label="Page Bottom"/>
        </container>
    </container>
    <!-- page content structure -->
    <referenceContainer name="seller.main.content">
        <block class="Magento\Framework\View\Element\Template" name="skip_to_content.target" before="-" template="Magento_Theme::html/skiptarget.phtml">
            <arguments>
                <argument name="target_id" xsi:type="string">contentarea</argument>
            </arguments>
        </block>
    </referenceContainer>
    <referenceContainer name="seller.columns.top">
        <block class="Magento\Theme\Block\Html\Title" name="seller.page.main.title" template="Magento_Theme::html/title.phtml"/>
        <container name="seller.page.messages" htmlTag="div" htmlClass="page messages">
            <block class="Magento\Framework\View\Element\Template" name="seller.ajax.message.placeholder" template="Magento_Theme::html/messages.phtml"/>
            <block class="Magento\Framework\View\Element\Messages" name="seller.messages" as="seller_messages" template="Magento_Theme::messages.phtml"/>
        </container>
    </referenceContainer>
    <referenceContainer name="seller.main">
        <container name="seller.content.top" label="Main Content Top"/>
        <container name="seller.content" label="Main Content Area"/>
        <container name="seller.content.aside" label="Main Content Aside"/>
        <container name="seller.content.bottom" label="Main Content Bottom"/>
    </referenceContainer>
    <referenceContainer name="seller.page.bottom.container">
        <container name="seller.page.bottom" label="Before Page Footer" htmlTag="div" htmlClass="content"/>
    </referenceContainer>
    <referenceContainer name="seller.content">
        <block class="Magento\Framework\View\Element\FormKey" name="seller.formkey"/>
        <block class="Magento\PageCache\Block\Javascript" template="Magento_PageCache::javascript.phtml" name="seller.pageCache" as="seller.pageCache"/>
        <!-- For form submit error fix -->
        <block name="seller.customer.section.config" class="Magento\Customer\Block\SectionConfig"
                template="Magento_Customer::js/section-config.phtml">
                <arguments>
                    <argument name="sectionNamesProvider" xsi:type="object">Magento\Customer\Block\SectionNamesProvider</argument>
                </arguments>
        </block>
        <block name="seller.customer.customer.data"
                class="Magento\Customer\Block\CustomerData"
                template="Magento_Customer::js/customer-data.phtml">
        <arguments>
            <argument name="auth" xsi:type="object">Magento\Customer\ViewModel\Customer\Auth</argument>
            <argument name="json_serializer" xsi:type="object">Magento\Customer\ViewModel\Customer\JsonSerializer</argument>
            <argument name="cookie_settings" xsi:type="object">Magento\Customer\ViewModel\CookieSettings</argument>
        </arguments>
    </block>
    </referenceContainer>
    <!-- for seller page layout -->
    <referenceContainer name="seller.header.before.container">
        <block class="Webkul\Marketplace\Block\Page\Switcher" name="seller.top.header.block" template="Webkul_Marketplace::/layout2/page/switcher.phtml"/>
    </referenceContainer>
    <referenceContainer name="seller.top.header.block">
        <block class="Magento\Store\Block\Switcher" name="seller_store_language" as="seller_store_language" template="Magento_Store::switch/languages.phtml">
            <arguments>
                <argument name="view_model" xsi:type="object">Magento\Store\ViewModel\SwitcherUrlProvider</argument>
            </arguments>
        </block>
        <block class="Magento\Store\Block\Switcher" name="seller_store_switcher" as="seller_store_switcher" template="Magento_Store::switch/stores.phtml"/>
        <block class="Magento\Directory\Block\Currency" name="seller_store_settings_currency" as="seller_store_settings_currency" template="Magento_Directory::currency.phtml"/>
    </referenceContainer>
    <referenceContainer name="seller.header">
        <block class="Webkul\Marketplace\Block\Page\Header" name="seller.logo" before="-">
            <arguments>
                <argument name="show_part" xsi:type="string">logo</argument>
            </arguments>
        </block>
        <block class="Webkul\Marketplace\Block\Page\Header" name="seller.user" after="-">
            <arguments>
                <argument name="show_part" xsi:type="string">user</argument>
            </arguments>
        </block>
        <block class="Webkul\Marketplace\Block\Page\Notifications" name="seller.notifications" after="-">
            <arguments>
                <argument name="show_part" xsi:type="string">seller.notifications</argument>
            </arguments>
        </block>
        <block class="Webkul\Marketplace\Block\Page\Header" name="seller.home" after="-">
            <arguments>
                <argument name="show_part" xsi:type="string">seller.home</argument>
            </arguments>
        </block>
        <block class="Webkul\Marketplace\Block\Page\Header" name="seller.assistance" after="-">
            <arguments>
                <argument name="show_part" xsi:type="string">seller.assistance</argument>
            </arguments>
        </block>
    </referenceContainer>
    <referenceContainer name="seller.page.menu">
        <block class="Webkul\Marketplace\Block\Account\Navigation" name="layout2_seller_account_navigation_main" before="-" template="Webkul_Marketplace::/layout2/account/navigation.phtml">
            <block class="Magento\Framework\View\Element\Template" name="layout2_seller_account_navigation" template="Webkul_Marketplace::/layout2/account/navigation/child_menu.phtml"/>
            <block class="Webkul\Marketplace\Block\Account\Navigation" name="layout2_seller_account_navigation_settings_menu" template="Webkul_Marketplace::/layout2/account/navigation/settings_menu.phtml">
                <block class="Webkul\Marketplace\Block\Account\Navigation\ShippingMenu" name="layout2_seller_account_navigation_shipping_menu" template="Webkul_Marketplace::/layout2/account/navigation/shipping_menu.phtml"/>
                <block class="Webkul\Marketplace\Block\Account\Navigation\PaymentMenu" name="layout2_seller_account_navigation_payment_menu" template="Webkul_Marketplace::/layout2/account/navigation/payment_menu.phtml"/>
            </block>
        </block>
    </referenceContainer>
    <!-- for seller page header and menu position -->
    <referenceContainer name="seller.header" htmlClass="wk-mp-page-header wk-mp-row">
        <container name="seller.header.inner.left" before="-" htmlTag="div" htmlClass="wk-mp-page-header-hgroup col-l-8 col-m-6"/>
        <container name="seller.header.inner.right" after="seller.header.inner.left" htmlTag="div" htmlClass="wk-mp-page-header-actions col-l-4 col-m-6"/>
    </referenceContainer>

    <move element="seller.page.menu" destination="seller.menu.wrapper" />
    <move element="seller.logo" before="-" destination="seller.menu.wrapper" />
    <move element="seller.page.main.title" before="-" destination="seller.header.inner.left" />
    <move element="seller.user" before="-" destination="seller.header.inner.right" />
    <move element="seller.notifications" after="seller.user" destination="seller.header.inner.right" />
    <move element="seller.home" after="seller.notifications" destination="seller.header.inner.right" />
    <move element="seller.assistance" after="seller.home" destination="seller.header.inner.right" />
    <remove name="page.main.title"/>
</layout>
